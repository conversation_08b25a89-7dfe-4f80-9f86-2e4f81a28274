:IF: Colony GUI - v1.0.0 RELEASED!

After months of work, the first release of Colony is here! Read more below, or watch the video!

### LINK TO YOUTUBE VIDEO HERE

## ✨ Features

- 🔐 **Secure Wallet Management** - BIP39 seed phrase generation and secure key storage
- 📁 **File Upload/Download** - Upload and download files easily
- 🔍 **Metadata Search** - Client-side search using simple text search
- 📁 **Pod Management** - Create and manage metadata for organizing and sharing content
- 🔗 **Decentralized Sharing** - Share files and metadata without a central database
- 🖥️ **Cross-Platform** - Native applications for Linux, Windows, and macOS*
- 🌐 **Dweb Built-in** - Dweb binary built-in for automatically opening Autonomi web apps
-    **Local First** - For download only users, no crypto is required, install and go

## 📦 Installation

Precompiled binaries are available for various flavors of Linux as well as Windows on the github releases page. For our Mac friends we are unable to build binaries that will run on your computer due to Apple's ridicul... _wonderful_ "safety" features. The application does work well on a Mac, you just need to compile the program yourself. See the [README](https://github.com/zettawatt/colony/blob/main/README.md). One day I may shell out the $99 for the privilege of having some Apple certificates. Maybe.

## 🚀 Getting Started

After the initial setup, the first thing a user needs to do is add a pod. This is where the metadata for any files you upload will be stored and where you will store references to other users' pods.

### ADD POD GIF HERE

Next you'll want to add a reference to a pod that contains metadata and links to other pods. This basically bootstraps you into the existing network. The first pod on Colony is called the Genesis Pod, and its address is this:

`aaa518a2cf8260f6bebc769c16b8147ea215adf569696497b7fc1f250823d89a49990187e83fc0f9ae1cf3d44afb7dce`

Add this as a reference to the pod you just added and hit Save:

### ADD POD REFERENCE GIF HERE

After a lot of scraping and searching I believe I have found everything that there is to find on the network that folks have posted on the forum. This pod also contains a collection of public domain 'banned' books from the Internet Archive, just so there is more to search on the network. It's pretty lean right now! If you want to help populate data and are command line savy, check out the companion suite of tools [colony-utils](https://github.com/zettawatt/colony-utils)!

Now you can go to the Search tab. You can type a search query or just hit the Enter key to browse everything on the network. Its just that easy.

### ADD BROWSE GIF HERE

The info icon will show some of the metadata stored for that particular object while the download icon will download it into your specified downloads directory. Except for Autonomi web apps, those are special...

## Dweb baked in

I added the latest dweb binaries as a side car in the Colony app. It automatically starts up when you login to the app. When you click on the download icon for a web app, it calls `dweb open` for you and pulls up the page in your web browser. Simple.

Your active wallet is used to initialize dweb. Note that if you switch active wallets, dweb will restart using that new wallet. So if you have web apps open when you switch wallets, that could be a problem, just an FYI.

## But wait, there's more!

- **Wallet Management** - Manage multiple wallets for upload payments
- **Configuration** - download directory, change password, themes
- **Status Tracking** - track your upload and download progress

## 🔧 What's left?

We had to stop work to get this stable release out so the last week we've been crushing known bugs and getting to a stable point. The major enhancments we're still working on:
- **Download Streaming** - track progress and estimate remaining time
- **Search Interface** - show more information per searched item with thumbnail support
- **Object Info** - show more information about individual objects before downloading
- **Metadata Helpers** - leverage `ia_downloader` mechanism for metadata enhancement
- **Wallet Balance** - simple to do, just didn't get to it yet
- **Error Handling** - better Autonomi error handling

## We made it!

It has been a long journey with lots of sleepless nights and non-stop work, but we got this thing out by the deadline. In addition, I submit to the IF judges a whole suite of cross platform tools that lay the foundation and enhances the capabilities of the Colony GUI app:

- **[colonylib](https://github.com/zettawatt/colonylib)** - core functionality crate that others are using successfully such as [Mutant]() for content discovery
- **[colonyd](https://github.com/zettawatt/colony-utils)** - a daemon exposing the colonylib API as REST endpoints
- **[colony](https://github.com/zettawatt/colony-utils)** - a colony CLI implementation that interacts with colonyd, for advanced users and scripting
- **[ia_downloader](https://github.com/zettawatt/colony-utils)** - Internet Archive bulk downloader and metadata composition tool
- **[colony_uploader](https://github.com/zettawatt/colony-utils)** - bulk data Autonomi uploader with metrics tracking

There is more to do, but I'm very proud of what we've accomplished so far. So give it a try, let us know what works, what doesn't, and what could be better. Thank you everyone for your support so far, for today, finally, we can _Search Autonomi Simply_.
